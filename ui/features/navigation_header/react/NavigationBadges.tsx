/*
 * Copyright (C) 2015 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import {useEffect} from 'react'
import {useScope as createI18nScope} from '@canvas/i18n'
import {ScreenReaderContent, PresentationContent} from '@instructure/ui-a11y-content'
import {Portal} from '@instructure/ui-portal'
import {useQuery, queryClient} from '@canvas/query'
import {getUnreadCount} from './queries/unreadCountQuery'


import {getSetting} from '@canvas/settings-query/react/settingsQuery'

// Type declaration for ActionCable
declare module '@rails/actioncable' {
  export function createConsumer(url?: string): any
}

const I18n = createI18nScope('Navigation')

const unreadReleaseNotesCountElement = document.querySelector(
  '#global_nav_help_link .menu-item__badge',
)
const unreadInboxCountElement = document.querySelector(
  '#global_nav_conversations_link .menu-item__badge',
)
const unreadSharesCountElement = document.querySelector(
  '#global_nav_profile_link .menu-item__badge',
)
const unreadChatCountElement = document.querySelector(
  '#global_nav_chat_badge',
)

export default function NavigationBadges() {
  const countsEnabled = Boolean(
    window.ENV.current_user_id && !window.ENV.current_user?.fake_student,
  )

  const {data: releaseNotesBadgeDisabled} = useQuery({
    queryKey: ['settings', 'release_notes_badge_disabled'],
    queryFn: getSetting,
    enabled: countsEnabled && ENV.FEATURES.embedded_release_notes,
    meta: {
      fetchAtLeastOnce: true,
    },
  })

  const {data: unreadContentSharesCount, isSuccess: hasUnreadContentSharesCount} = useQuery({
    queryKey: ['unread_count', 'content_shares'],
    queryFn: getUnreadCount,
    staleTime: 60 * 60 * 1000, // 1 hour
    enabled: countsEnabled && ENV.CAN_VIEW_CONTENT_SHARES,
    refetchOnWindowFocus: true,
  })

  const {data: unreadConversationsCount, isSuccess: hasUnreadConversationsCount} = useQuery({
    queryKey: ['unread_count', 'conversations'],
    queryFn: getUnreadCount,
    staleTime: 2 * 60 * 1000, // two minutes
    enabled: countsEnabled && !ENV.current_user_disabled_inbox,
    meta: {
      broadcast: true,
    },
    refetchOnWindowFocus: true,
  })

  const {data: unreadReleaseNotesCount, isSuccess: hasUnreadReleaseNotesCount} = useQuery({
    queryKey: ['unread_count', 'release_notes'],
    queryFn: getUnreadCount,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    enabled: countsEnabled && ENV.FEATURES.embedded_release_notes && !releaseNotesBadgeDisabled,
  })

  const getChatUnreadCount = async () => {
    const response = await fetch('/api/v1/chat/unread_count', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      },
      credentials: 'same-origin'
    })

    if (!response.ok) {
      throw new Error('Failed to fetch chat unread count')
    }

    const data = await response.json()
    return data.success ? data.unread_count : 0
  }

  const {data: unreadChatCount, isSuccess: hasUnreadChatCount, error: chatCountError} = useQuery({
    queryKey: ['unread_count', 'chat'],
    queryFn: getUnreadCount,
    staleTime: 60 * 1000, // 1 minute (longer since we have real-time updates)
    enabled: countsEnabled,
    refetchInterval: 60 * 1000, // Fallback polling every minute
    refetchOnWindowFocus: true,
  })

  // Directly manipulate the badge element when count changes
  useEffect(() => {
    if (hasUnreadChatCount && unreadChatCount !== undefined) {
      const badgeElement = document.getElementById('global_nav_chat_badge')

      // Check if we're currently on the chat page
      const isOnChatPage = window.location.pathname === '/chat' || window.location.pathname.startsWith('/chat/')

      if (badgeElement) {
        if (unreadChatCount > 0 && !isOnChatPage) {
          // Show the badge with exclamation mark only if not on chat page
          badgeElement.textContent = '!'
          badgeElement.style.display = 'inline-flex'
          badgeElement.style.alignItems = 'center'
          badgeElement.style.justifyContent = 'center'
          badgeElement.style.position = 'absolute'
          badgeElement.style.top = '-8px'
          badgeElement.style.right = '-8px'
          badgeElement.style.backgroundColor = '#e74c3c'
          badgeElement.style.color = 'white'
          badgeElement.style.borderRadius = '50%'
          badgeElement.style.minWidth = '18px'
          badgeElement.style.height = '18px'
          badgeElement.style.fontSize = '14px'
          badgeElement.style.fontWeight = 'bold'
          badgeElement.style.lineHeight = '1'
          badgeElement.style.zIndex = '1000'
          badgeElement.style.border = '2px solid white'
        } else {
          // Hide the badge if no unread messages OR if on chat page
          badgeElement.style.display = 'none'
        }
      } else {
        console.warn('NavigationBadges: Badge element not found in DOM')
      }
    }
  }, [unreadChatCount, hasUnreadChatCount])

  // Set up ActionCable for real-time chat notifications
  useEffect(() => {
    if (!countsEnabled || !window.ENV?.current_user_id) {
      return
    }

    let cable: any = null
    let subscription: any = null

    const setupActionCable = async () => {
      try {
        // Import ActionCable dynamically
        const {createConsumer} = await import('@rails/actioncable')
        cable = createConsumer('/cable')

        // Subscribe to global notifications
        subscription = cable.subscriptions.create(
          {
            channel: 'GlobalNotificationChannel',
            user_id: window.ENV.current_user_id,
          },
          {
            connected() {
              console.log('✅ NavigationBadges: Connected to GlobalNotificationChannel for chat badges')
            },
            disconnected() {
              console.log('❌ NavigationBadges: Disconnected from GlobalNotificationChannel')
            },
            rejected() {
              console.error('❌ NavigationBadges: GlobalNotificationChannel subscription rejected')
            },
            received(data: any) {
              console.log('📢 NavigationBadges: Received global notification:', data)

              if (data.type === 'new_message_notification') {
                // Invalidate and refetch the chat unread count
                queryClient.invalidateQueries(['unread_count', 'chat'])
                console.log('🔄 NavigationBadges: Invalidated chat unread count query due to new message')
              }
            },
          }
        )
      } catch (error) {
        console.warn('NavigationBadges: Failed to setup ActionCable for chat notifications:', error)
        console.warn('NavigationBadges: Falling back to polling only')
      }
    }

    setupActionCable()

    // Cleanup function
    return () => {
      if (subscription) {
        subscription.unsubscribe()
      }
      if (cable) {
        cable.disconnect()
      }
    }
  }, [countsEnabled])

  return (
    <>
      <Portal
        open={hasUnreadContentSharesCount && unreadContentSharesCount > 0}
        mountNode={unreadSharesCountElement}
      >
        <ScreenReaderContent>
          <>
            {I18n.t(
              {
                one: 'One unread share.',
                other: '%{count} unread shares.',
              },
              {count: unreadContentSharesCount},
            )}
          </>
        </ScreenReaderContent>
        <PresentationContent>{unreadContentSharesCount}</PresentationContent>
      </Portal>

      <Portal
        open={hasUnreadConversationsCount && unreadConversationsCount > 0}
        mountNode={unreadInboxCountElement}
      >
        <ScreenReaderContent>
          <>
            {I18n.t(
              {
                one: 'One unread message.',
                other: '%{count} unread messages.',
              },
              {count: unreadConversationsCount},
            )}
          </>
        </ScreenReaderContent>
        <PresentationContent>{unreadConversationsCount}</PresentationContent>
      </Portal>

      <Portal
        open={hasUnreadReleaseNotesCount && unreadReleaseNotesCount > 0}
        mountNode={unreadReleaseNotesCountElement}
      >
        <ScreenReaderContent>
          <>
            {I18n.t(
              {
                one: 'One unread release note.',
                other: '%{count} unread release notes.',
              },
              {count: unreadReleaseNotesCount},
            )}
          </>
        </ScreenReaderContent>
        <PresentationContent>{unreadReleaseNotesCount}</PresentationContent>
      </Portal>
    </>
  )
}
