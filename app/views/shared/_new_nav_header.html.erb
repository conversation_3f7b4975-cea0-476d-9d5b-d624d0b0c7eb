<%
  # Copyright (C) 2015 - present Instructure, Inc.
  #
  # This file is part of Canvas.
  #
  # Canvas is free software: you can redistribute it and/or modify it under
  # the terms of the GNU Affero General Public License as published by the Free
  # Software Foundation, version 3 of the License.
  #
  # Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
  # WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
  # A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
  # details.
  #
  # You should have received a copy of the GNU Affero General Public License along
  # with this program. If not, see <http://www.gnu.org/licenses/>.
%>
<% js_bundle :navigation_header %>
<% if @domain_root_account && @domain_root_account.feature_enabled?(:product_tours) %>
  <% js_bundle :nav_tourpoints %>
<% end %>
<% if !!@domain_root_account&.feature_enabled?(:top_navigation_placement) %>
  <% js_bundle :top_navigation_tools %>
<% end %>
<%
  if k5_user?
    dashboard_title = t('Home')
    dashboard_icon_path = "shared/svg/svg_icon_home"
    courses_title = t('Subjects')
  else
    dashboard_title = t('Dashboard')
    dashboard_icon_path = "shared/svg/#{'k12/' if k12?}svg_icon_dashboard"
    courses_title = t('Courses')
  end
  student_view_label = @context.is_a?(Course) && @context.horizon_course? ? t('View as Learner') : t('View as Student')
%>
<header id="mobile-header" class="no-print">
  <button type="button" class="Button Button--icon-action-rev Button--large mobile-header-hamburger">
    <i class="icon-solid icon-hamburger"></i>
    <span id="mobileHeaderInboxUnreadBadge" class="menu-item__badge" style="min-width: 0; top: 12px; height: 12px; right: 6px; display:none;"></span>
    <span class="screenreader-only"><%= t('global_navigation_menu', "Global Navigation Menu") %></span>
  </button>
  <div class="mobile-header-space"></div>
  <% context_crumb = crumbs.length > 1 && crumbs[1] %>
  <% if context_crumb %>
    <a class="mobile-header-title expandable" href="<%= context_crumb[1] %>" role="button" aria-controls="mobileContextNavContainer">
      <div><%= context_crumb[0] %></div>
      <% if crumbs.length > 2 %>
        <% sub_context_crumb = crumbs.last %>
        <div><%= sub_context_crumb[0] %></div>
      <% end %>
    </a>
  <% else %>
    <span class="mobile-header-title"><%= (yield :page_title).presence || @page_title || t('default_page_title', "Canvas LMS") %></span>
  <% end %>
  <% if show_immersive_reader? %>
    <div id="immersive_reader_mobile_mount_point"></div>
  <% end %>
  <% if show_student_view_button? %>
    <%= link_to course_student_view_path(course_id: @context, redirect_to_referer: 1), :class => "Button Button--icon-action-rev Button--large mobile-header-student-view", :id => "mobile-student-view", :"aria-label" => student_view_label, :role => "button", :method => :post do %>
      <i class="icon-student-view"></i>
    <% end %>
  <% else %>
    <div class="mobile-header-space"></div>
  <% end %>
  <% if !!@domain_root_account&.feature_enabled?(:top_navigation_placement) %>
    <div id="mobile-top-nav-tools-mount-point"></div>
  <% end %>
  <% if show_blueprint_button? %>
    <button class="mobile-header-blueprint-button Button Button--icon-action-rev Button--large" aria-label="<%= t "Open Blueprint Sidebar" %>">
      <i class="icon-blueprint"></i>
    </button>
  <% end %>
  <% if context_crumb %>
    <button type="button" class="Button Button--icon-action-rev Button--large mobile-header-arrow" aria-label="<%= t "Navigation Menu" %>">
      <i class="icon-arrow-open-down" id="mobileHeaderArrowIcon"></i>
    </button>
  <% else %>
    <div class="mobile-header-space"></div>
  <% end %>
</header>
<nav id="mobileContextNavContainer"></nav>

<header id="header" class="ic-app-header no-print <%= 'no-user' unless @current_user %> primary-nav-expanded-default" aria-label="<%= t('Global Header') %>">
  <a href="#content" id="skip_navigation_link"><%= t 'links.skip_to_content', "Skip To Content" %></a>
  <div role="region" class="ic-app-header__main-navigation" aria-label="<%= t('Global Navigation') %>">
    <ul id="menu" class="ic-app-header__menu-list">
      <% if @current_user %>
        <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/profile') %>">
          <a id="global_nav_profile_direct_link" href="/profile" class="ic-app-header__menu-list-link" onclick="window.location.href='/profile'; return false;">
            <div class="menu-item-icon-container">
              <div aria-hidden="true" class="fs-exclude ic-avatar <% if @real_current_user && @real_current_user != @current_user %>ic-avatar--fake-student<% end %>">
                <img src="<%= @current_user.try { |usr| avatar_image_attrs(usr).first } %>" alt="<%= @current_user.short_name %>" />
              </div>
              <span class="menu-item__badge"></span>
            </div>
            <div class="menu-item__text">
              <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 120px;">
                <%= @current_user.short_name %>
              </div>
              <div style="font-size: 0.8em; opacity: 0.8; color: #f4b952;">
                <% 
                  role_text = if @current_user.adminable_accounts.present?
                    'Admin'
                  elsif @current_user.respond_to?(:primary_enrollment) && @current_user.primary_enrollment
                    case @current_user.primary_enrollment.type
                    when 'StudentEnrollment'
                      'Student'
                    when 'TeacherEnrollment', 'TaEnrollment'
                      'Teacher'
                    when 'ObserverEnrollment'
                      'Parent'
                    else
                      'User'
                    end
                  elsif @current_user.respond_to?(:enrollments) && @current_user.enrollments.active.first
                    case @current_user.enrollments.active.first.type
                    when 'StudentEnrollment'
                      'Student'
                    when 'TeacherEnrollment', 'TaEnrollment'
                      'Teacher'
                    when 'ObserverEnrollment'
                      'Parent'
                    else
                      'User'
                    end
                  else
                    'User'
                  end
                %>
                <%= role_text %>
              </div>
            </div>
          </a>
        </li>
      <% else %>
        <li class="menu-item ic-app-header__menu-list-item">
          <a id="global_nav_login_link" href="/login" class="ic-app-header__menu-list-link">
            <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/svg_login_new_styles", formats: [:svg]) %>
            </div>
            <div class="menu-item__text">
              <%= t('Login') %>
            </div>
          </a>
        </li>
      <% end %>
      <% if @current_user %>
        <li class="menu-item ic-app-header__menu-list-item" style="pointer-events: none; padding: 0; margin: 8px 0;">
          <div style="height: 1px; background-color: rgba(255, 255, 255, 0.2); margin: 0 16px;"></div>
        </li>
      <% end %>
      <% if @current_user && @current_user.adminable_accounts.present? %>
        <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/accounts') %>">
          <a id="global_nav_accounts_link" role="button" href="/accounts" class="ic-app-header__menu-list-link">
            <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/svg_icon_accounts_new_styles", formats: [:svg]) %>
            </div>
            <div class="menu-item__text">
              <%= t('Admin') %>
            </div>
          </a>
        </li>
      <% end %>
      <li class="ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if current_page?(dashboard_url) %>">
        <a id="global_nav_dashboard_link" href="<%= dashboard_url %>" class="ic-app-header__menu-list-link">
          <div class="menu-item-icon-container" aria-hidden="true">
            <%= render(partial: dashboard_icon_path, formats: [:svg]) %>
          </div>
          <div class="menu-item__text">
            <%= dashboard_title %>
          </div>
        </a>
      </li>
      <% if @current_user %>
        <li class="menu-item ic-app-header__menu-list-item <%= 'ic-app-header__menu-list-item--active' if active_path?('/courses') %>">
          <a id="global_nav_courses_direct_link" href="/courses" class="ic-app-header__menu-list-link" onclick="window.location.href='/courses'; return false;">
            <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/#{'k12/' if k12?}svg_icon_courses_new_styles", formats: [:svg]) %>
            </div>
            <div class="menu-item__text">
              <%= courses_title %>
            </div>
          </a>
        </li>
      <% end %>
      <% if @current_user && @current_user.current_active_groups? %>
        <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/groups') %>">
          <a id="global_nav_groups_link" role="button" href="/groups" class="ic-app-header__menu-list-link">
            <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/svg_icon_groups_new_styles", formats: [:svg]) %>
            </div>
            <div class="menu-item__text">
              <%= t('Groups') %>
            </div>
          </a>
        </li>
      <% end %>
      <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/calendar') %>">
        <a id="global_nav_calendar_link" href="/calendar" class="ic-app-header__menu-list-link">
          <div class="menu-item-icon-container" aria-hidden="true">
            <%= render(partial: "shared/svg/#{'k12/' if k12?}svg_icon_calendar_new_styles", formats: [:svg]) %>
          </div>
          <div class="menu-item__text">
            <%= t('Calendar') %>
          </div>
        </a>
      </li>
      <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/conversations') %>">
      <!-- TODO: Add back global search when available -->
        <a id="global_nav_conversations_link" href="/conversations" class="ic-app-header__menu-list-link">
          <div class="menu-item-icon-container">
            <span aria-hidden="true"><%= render(partial: "shared/svg/#{'k12/' if k12?}svg_icon_inbox", formats: [:svg]) %></span>
            <span class="menu-item__badge"></span>
          </div>
          <div class="menu-item__text">
            <%= t('Inbox') %>
          </div>
        </a>
      </li>
      <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/chat') %>">
        <a id="global_nav_chat_link" class="ic-app-header__menu-list-link" href="/chat">
           <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/svg_icon_mail_new_styles", formats: [:svg]) %>
              <span id="global_nav_chat_badge" class="menu-item__badge" style="display: none;"></span>
            </div>
          <div class="menu-item__text">
            <%= t('Chat') %>
          </div>
        </a>
      </li>
      <li class="menu-item ic-app-header__menu-list-item <%= ' ic-app-header__menu-list-item--active' if active_path?('/e-resources') %>">
        <a id="global_nav_eresources_link" class="ic-app-header__menu-list-link" href="/e-resources">
          <div class="menu-item-icon-container" aria-hidden="true">
            <%= render(partial: "shared/svg/svg_icon_eresource", formats: [:svg]) %>
          </div>
          <div class="menu-item__text">
            <%= t('E-resources') %>
          </div>
        </a>
      </li>
      <% if PageView.page_views_enabled? %>
        <li class="menu-item ic-app-header__menu-list-item" <%= ' ic-app-header__menu-list-item--active' if active_path?('/history') %>>
          <a id="global_nav_history_link" role="button" href="#" class="ic-app-header__menu-list-link">
            <div class="menu-item-icon-container" aria-hidden="true">
              <%= render(partial: "shared/svg/svg_icon_history", formats: [:svg]) %>
            </div>
            <div class="menu-item__text">
              <%= t('History') %>
            </div>
          </a>
        </li>
      <% end %>
      <% unless @current_user.nil? %>
        <%= render(partial: 'external_tools/global_nav_menu_items') %>
      <% end %>
      
    </ul>
  </div>
  
  <div id="global_nav_tray_container"></div>
  <div id="global_nav_tour"></div>
</header>