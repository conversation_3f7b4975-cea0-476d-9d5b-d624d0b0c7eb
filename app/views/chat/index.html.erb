<%
  content_for :page_title, t('Chat')
  content_for :right_side do
%>
  <div class="chat-sidebar-right">
    <div class="chat-sidebar-header">
      <div class="chat-header-content">
        <i class="icon-solid icon-message chat-icon"></i>
        <h3><%= t('Users') %></h3>
      </div>
    </div>
    <div class="chat-sidebar-spacer"></div>
    <div class="chat-users-list">
      <!-- User list will be populated here via JavaScript -->
      <div class="loading-users">
        <i class="icon-solid icon-refresh icon-spin"></i>
        <p><%= t('Loading users...') %></p>
      </div>
    </div>
    <div class="chat-search-area">
      <input type="text" id="chat-search" placeholder="<%= t('Search users...') %>" class="chat-search-input">
    </div>
  </div>
<% end %>

<div id="chat-page-container">
  <div class="chat-page-header">
    <h1 class="chat-page-title">
      <i class="icon-solid icon-message"></i>
      <%= t('Chat') %>
    </h1>
    <p class="chat-page-subtitle"><%= t('Connect with your classmates and instructors') %></p>
  </div>

  <div class="chat-main-container">
    <div class="chat-main">
      <div class="chat-header">
        <div class="chat-header-user">
          <div class="selected-user-avatar">
            <img id="selected-user-avatar" src="" alt="" style="display: none;">
          </div>
          <div class="selected-user-info">
            <h3 id="selected-user-name"><%= t('Select a user to start chatting') %></h3>
            <span id="selected-user-status" class="user-status-text"></span>
          </div>
        </div>
      </div>
      <div class="chat-messages" id="chat-messages">
        <div class="chat-placeholder">
          <i class="icon-solid icon-message"></i>
          <p><%= t('Select a user to start chatting') %></p>
        </div>
      </div>
      <div class="chat-input-area">
        <form id="chat-form" class="">
          <input type="hidden" id="recipient-id" value="">
          <input type="text" id="chat-input" placeholder="<%= t('Type your message here...') %>" disabled>
          <button type="submit" id="send-button" disabled>
            <i class="icon-solid icon-arrow-end"></i>
          </button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
  // Pass current user data to JavaScript
  window.chatConfig = {
    currentUserId: <%= @current_user_id %>,
    currentUserName: '<%= j(@current_user_name) %>',
    currentUserAvatar: '<%= j(@current_user_avatar) %>'
  };
</script>

<%= action_cable_meta_tag %>

<% js_bundle :chat_page %>

<style>
  #chat-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 20px 10px 20px;
    background-color: #f8f9fa;
    min-height: calc(100vh - 110px);
  }
  
  .chat-page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  .chat-page-title {
    font-size: 28px;
    color: #2D7D32;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }
  
  .chat-page-title i {
    font-size: 32px;
  }
  
  .chat-page-subtitle {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
  
  .chat-main-container {
    height: calc(100vh - 400px);
    min-height: 400px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
  }

  /* Right sidebar styles for chat users */
  .chat-sidebar-right {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 220px);
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    overflow: hidden;
    min-height: 580px;
  }

  .chat-sidebar-spacer {
    flex: 1;
  }

  .chat-sidebar-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    background-color: #2D7D32;
    color: white;
  }

  .chat-header-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chat-icon {
    font-size: 18px;
  }

  .chat-sidebar-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .chat-users-list {
    flex: 0 0 auto;
    overflow-y: auto;
    padding: 0;
    max-height: 300px;
  }

  #chat-search {
    width: 190px;
  }

  .chat-search-area {
    padding: 15px;
    border-top: 1px solid #e5e5e5;
    background-color: white;
  }

  .chat-search-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
  }

  .chat-search-input:focus {
    outline: none;
    border-color: #2D7D32;
    box-shadow: 0 0 0 2px rgba(45, 125, 50, 0.1);
  }
  
  .loading-users {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
  }
  
  .loading-users i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #2D7D32;
  }
  
  .icon-spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .chat-user-item {
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    position: relative;
  }
  
  .chat-user-item:hover {
    background-color: #e8f5e8;
  }
  
  .chat-user-item.active {
    background-color: #c8e6c9;
    border-left: 4px solid #2D7D32;
  }

  /* Notification styles */
  .chat-user-item .user-unread-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #fff;
    border-radius: 50%;
    padding: 2px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }

  .chat-user-item.has-unread {
    background-color: #d4edda !important;
    border-left: 4px solid #28a745 !important;
  }
  
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
    border: 2px solid #e5e5e5;
  }
  
  .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .user-name {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
  }
  
  .user-status-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .user-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 1px #e5e5e5;
  }
  
  .user-status.online {
    background-color: #4CAF50;
  }
  
  .user-status.offline {
    background-color: #9e9e9e;
  }
  
  .user-status-text {
    font-size: 12px;
    color: #666;
  }
  
  .chat-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: white;
  }
  
  .chat-header {
    padding: 20px;
    border-bottom: 1px solid #e5e5e5;
    background-color: white;
  }
  
  .chat-header-user {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .selected-user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e5e5e5;
  }
  
  .selected-user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .selected-user-info h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .user-status-text {
    font-size: 13px;
    color: #666;
    margin-top: 2px;
  }
  
  .chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #fafafa;
    background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  .chat-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    text-align: center;
  }
  
  .chat-placeholder i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .chat-placeholder p {
    font-size: 16px;
    margin: 0;
  }
  
  .message {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    animation: fadeIn 0.3s ease-in;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .message.sent {
    align-items: flex-end;
  }
  
  .message.received {
    align-items: flex-start;
  }
  
  .message-content {
    max-width: 75%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
  }
  
  .message.sent .message-content {
    background-color: #2D7D32;
    color: white;
    border-bottom-right-radius: 4px;
  }
  
  .message.received .message-content {
    background-color: white;
    border: 1px solid #e5e5e5;
    color: #333;
    border-bottom-left-radius: 4px;
  }
  
  .message-time {
    font-size: 11px;
    margin-top: 4px;
    opacity: 0.7;
    color: #666;
  }
  
  .chat-input-area {
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    background-color: white;
  }
  
  #chat-form {
    display: flex;
    gap: 12px;
    align-items: center;
  }


  
  #chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 24px;
    font-size: 14px;
    resize: none;
    min-height: 20px;
    max-height: 100px;
    line-height: 1.4;
    margin: 0
  }
  
  #chat-input:focus {
    outline: none;
    border-color: #2D7D32;
    box-shadow: 0 0 0 2px rgba(45, 125, 50, 0.1);
  }
  
  #send-button {
    background-color: #2D7D32;
    color: white;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }
  
  #send-button:hover:not(:disabled) {
    background-color: #1B5E20;
  }
  
  #send-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  #send-button i {
    font-size: 18px;
    margin-left: 2px; /* Slight offset to visually center the arrow */
  }
  
  .no-users-found {
    text-align: center;
    padding: 20px;
    color: #666;
  }
  
  .no-users-found i {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.5;
  }

  /* Message status icons */
  .message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
  }

  .message-status-icon {
    font-size: 12px;
    margin-left: 6px;
    opacity: 0.7;
  }

  .message-status-icon.sent {
    color: #8B9DC3; /* Light blue for sent */
  }

  .message-status-icon.delivered {
    color: #28A745; /* Green for delivered */
  }

  .message-status-icon.seen {
    color: #007BFF; /* Blue for seen */
  }

  /* Only show status icons on sent messages */
  .message.received .message-status-icon {
    display: none;
  }

  /* Adjust message time styling when status icon is present */
  .message.sent .message-time {
    margin-right: auto;
  }
</style>
